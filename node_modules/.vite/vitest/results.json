{"version": "2.1.9", "results": [[":test/integration/end-to-end.test.ts", {"duration": 316.98533399999997, "failed": true}], [":test/core/translation-index.test.ts", {"duration": 23.05129199999999, "failed": false}], [":test/performance/benchmarks.test.ts", {"duration": 505.20799999999997, "failed": true}], [":test/core/file-watcher.test.ts", {"duration": 4185.58175, "failed": true}], [":test/server/mcp-server.test.ts", {"duration": 147.4011660000001, "failed": true}], [":test/tools/advanced-mcp-tools.test.ts", {"duration": 67.73183300000005, "failed": false}], [":test/integration/cli.test.ts", {"duration": 222.406208, "failed": true}], [":test/utils/json-operations.test.ts", {"duration": 57.54245800000001, "failed": false}], [":test/core/code-analyzer.test.ts", {"duration": 24.250459000000035, "failed": true}], [":test/tools/delete-translation-smart.test.ts", {"duration": 67.20854100000003, "failed": false}], [":test/utils/error-types.test.ts", {"duration": 11.256834000000026, "failed": false}], [":test/core/type-generator.test.ts", {"duration": 40.14195799999999, "failed": true}], [":test/integration/ide-workflow.test.ts", {"duration": 74.75858400000004, "failed": false}], [":test/core/translation-extractor.test.ts", {"duration": 19.461083000000002, "failed": false}], [":test/tools/search-missing-translations.test.ts", {"duration": 25.925250000000005, "failed": false}], [":test/utils/path-parser.test.ts", {"duration": 18.746542000000005, "failed": false}], [":test/tools/check-translation-integrity.test.ts", {"duration": 23.611041, "failed": false}], [":test/tools/reorganize-translation-files.test.ts", {"duration": 24.565834000000024, "failed": false}]]}