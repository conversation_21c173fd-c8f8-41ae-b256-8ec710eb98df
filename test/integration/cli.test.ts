/**
 * Tests for the main entry point and CLI functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { promises as fs } from 'fs';
import { join } from 'path';

// Mock the server module to avoid actual server startup
vi.mock('../../src/server/mcp-server.js', () => {
  const mockStart = vi.fn().mockResolvedValue(undefined);
  const mockStop = vi.fn().mockResolvedValue(undefined);
  
  return {
    TranslationMCPServer: vi.fn().mockImplementation(() => ({
      start: mockStart,
      stop: mockStop,
      getIndex: vi.fn().mockReturnValue({
        removeAllListeners: vi.fn()
      }),
      getFileWatcher: vi.fn().mockReturnValue({
        removeAllListeners: vi.fn()
      })
    }))
  };
});

describe('Main Entry Point', () => {
  let tempDir: string;
  let originalArgv: string[];
  let originalEnv: NodeJS.ProcessEnv;
  let consoleLogSpy: any;
  let consoleErrorSpy: any;
  let processExitSpy: any;

  beforeEach(async () => {
    tempDir = await globalThis.testUtils.createTempDir();
    await globalThis.testUtils.createTestTranslationFiles(tempDir);
    
    // Store original values
    originalArgv = process.argv;
    originalEnv = { ...process.env };
    
    // Setup spies
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    processExitSpy = vi.spyOn(process, 'exit').mockImplementation((code?: any) => {
      throw new Error(`Process exit called with code: ${code}`);
    });
  });

  afterEach(async () => {
    // Restore original values
    process.argv = originalArgv;
    process.env = originalEnv;

    // Restore spies
    vi.restoreAllMocks();

    await globalThis.testUtils.cleanupTempDir(tempDir);
  });

  // Helper function available to all tests
  const setupArgs = (args: string[]) => {
    process.argv = ['node', 'i18n-mcp', ...args];
  };

  describe('Command Line Argument Parsing', () => {

    it('should parse directory argument', async () => {
      setupArgs(['--dir', tempDir]);
      
      // Mock the import to avoid actual execution
      const { parseArgs } = await import('../../src/index.js');
      // This would normally not be accessible, so we'll test through the main function
    });

    it('should parse base language argument', async () => {
      setupArgs(['--base-language', 'es', tempDir]);
      
      // For now, we can't easily test the internal parseArgs function
      // In a real scenario, you might refactor to make it testable
    });

    it('should parse debug flag', async () => {
      setupArgs(['--debug', tempDir]);
      
      // Similar limitation - would need refactoring to test easily
    });

    it('should show help when requested', async () => {
      setupArgs(['--help']);
      
      try {
        // This will call process.exit, which we've mocked to throw
        await import('../../src/index.js');
      } catch (error) {
        expect(error.message).toContain('Process exit called with code: 0');
      }
      
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('i18n MCP Server - High-performance translation file management')
      );
    });

    it('should handle positional directory argument', async () => {
      setupArgs([tempDir]);
      
      // Test would require refactoring the main module to be more testable
    });
  });

  describe('Environment Variable Configuration', () => {
    it('should load configuration from environment variables', () => {
      process.env.I18N_MCP_DIR = tempDir;
      process.env.I18N_MCP_BASE_LANGUAGE = 'fr';
      process.env.I18N_MCP_DEBUG = 'true';
      
      // Test would require the loadEnvConfig function to be exported
    });

    it('should prioritize command line arguments over environment variables', () => {
      process.env.I18N_MCP_DIR = '/wrong/path';
      process.env.I18N_MCP_BASE_LANGUAGE = 'wrong';
      
      setupArgs(['--dir', tempDir, '--base-language', 'es']);
      
      // Test would require refactoring to make functions testable
    });
  });

  describe('Configuration Resolution', () => {
    it('should find default translation directories', async () => {
      // Create a default locales directory
      const defaultDir = join(process.cwd(), 'locales');
      await fs.mkdir(defaultDir, { recursive: true });
      await fs.writeFile(join(defaultDir, 'en.json'), '{}');
      
      try {
        setupArgs([]); // No directory specified
        
        // Test would require refactoring to access resolveConfig
      } finally {
        // Cleanup
        await fs.rm(defaultDir, { recursive: true, force: true });
      }
    });

    it('should error when no translation directory is found', async () => {
      setupArgs([]); // No directory and no default exists
      
      try {
        // This should cause an error and process.exit(1)
        await import('../../src/index.js');
      } catch (error) {
        expect(error.message).toContain('Process exit called with code: 1');
      }
      
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('No translation directory found')
      );
    });

    it('should error when specified directory does not exist', async () => {
      setupArgs(['--dir', '/nonexistent/directory']);
      
      try {
        await import('../../src/index.js');
      } catch (error) {
        expect(error.message).toContain('Process exit called with code: 1');
      }
      
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Translation directory does not exist')
      );
    });

    it('should error when specified path is not a directory', async () => {
      const filePath = join(tempDir, 'not-a-directory.txt');
      await fs.writeFile(filePath, 'this is a file');
      
      setupArgs(['--dir', filePath]);
      
      try {
        await import('../../src/index.js');
      } catch (error) {
        expect(error.message).toContain('Process exit called with code: 1');
      }
      
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Translation path is not a directory')
      );
    });
  });

  describe('Server Lifecycle', () => {
    it('should start server with valid configuration', async () => {
      setupArgs(['--dir', tempDir, '--base-language', 'en']);
      
      const { TranslationMCPServer } = await import('../../src/server/mcp-server.js');
      
      // Test would require a way to access the created server instance
      // For now, we can verify the mock was called correctly
      expect(TranslationMCPServer).toBeDefined();
    });

    it('should handle server startup errors', async () => {
      setupArgs(['--dir', tempDir]);
      
      // Mock server to throw error on start
      const { TranslationMCPServer } = await import('../../src/server/mcp-server.js');
      const mockInstance = new TranslationMCPServer({} as any);
      mockInstance.start = vi.fn().mockRejectedValue(new Error('Server start failed'));
      
      // This test would require refactoring to inject the server instance
    });
  });

  describe('Graceful Shutdown', () => {
    it('should handle SIGINT signal', async () => {
      const originalListeners = process.listeners('SIGINT');
      
      setupArgs(['--dir', tempDir]);
      
      try {
        // This would start the server and setup signal handlers
        // await import('../../src/index.js');
        
        // Verify signal handler was registered
        const currentListeners = process.listeners('SIGINT');
        expect(currentListeners.length).toBeGreaterThan(originalListeners.length);
        
      } finally {
        // Restore original listeners
        process.removeAllListeners('SIGINT');
        originalListeners.forEach(listener => process.on('SIGINT', listener));
      }
    });

    it('should handle SIGTERM signal', async () => {
      const originalListeners = process.listeners('SIGTERM');
      
      setupArgs(['--dir', tempDir]);
      
      try {
        // Similar to SIGINT test
        const currentListeners = process.listeners('SIGTERM');
        // Test signal handler registration
        
      } finally {
        process.removeAllListeners('SIGTERM');
        originalListeners.forEach(listener => process.on('SIGTERM', listener));
      }
    });

    it('should handle uncaught exceptions', async () => {
      const originalListeners = process.listeners('uncaughtException');
      
      setupArgs(['--dir', tempDir]);
      
      try {
        // Test uncaught exception handler
      } finally {
        process.removeAllListeners('uncaughtException');
        originalListeners.forEach(listener => process.on('uncaughtException', listener));
      }
    });

    it('should handle unhandled promise rejections', async () => {
      const originalListeners = process.listeners('unhandledRejection');
      
      setupArgs(['--dir', tempDir]);
      
      try {
        // Test unhandled rejection handler
      } finally {
        process.removeAllListeners('unhandledRejection');
        originalListeners.forEach(listener => process.on('unhandledRejection', listener));
      }
    });
  });

  describe('Help and Version Display', () => {
    it('should display help with --help flag', async () => {
      setupArgs(['--help']);
      
      try {
        await import('../../src/index.js');
      } catch (error) {
        expect(error.message).toContain('Process exit called with code: 0');
      }
      
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Usage: i18n-mcp [options] [translation-directory]')
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Options:')
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Examples:')
      );
    });

    it('should display help with -h flag', async () => {
      setupArgs(['-h']);
      
      try {
        await import('../../src/index.js');
      } catch (error) {
        expect(error.message).toContain('Process exit called with code: 0');
      }
      
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('i18n MCP Server - High-performance translation file management')
      );
    });

    it('should show error for unknown options', async () => {
      setupArgs(['--unknown-option']);
      
      try {
        await import('../../src/index.js');
      } catch (error) {
        expect(error.message).toContain('Process exit called with code: 1');
      }
      
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Unknown option: --unknown-option')
      );
    });
  });

  describe('Debug Mode', () => {
    it('should enable debug logging when --debug flag is used', async () => {
      setupArgs(['--debug', '--dir', tempDir]);
      
      // Test would require access to the configuration object
      // to verify debug was set to true
    });

    it('should show configuration in debug mode', async () => {
      setupArgs(['--debug', '--dir', tempDir]);
      
      try {
        // await import('../../src/index.js');
        
        // In debug mode, should log configuration
        expect(consoleLogSpy).toHaveBeenCalledWith(
          expect.stringContaining('Configuration:')
        );
      } catch (error) {
        // May exit during startup in test environment
      }
    });
  });

  describe('Default Directory Discovery', () => {
    it('should try multiple default directory names', async () => {
      const defaultPaths = ['locales', 'i18n', 'translations', 'lang'];
      
      // Create one of the default directories
      const testDir = join(process.cwd(), 'i18n');
      await fs.mkdir(testDir, { recursive: true });
      await fs.writeFile(join(testDir, 'en.json'), '{}');
      
      try {
        setupArgs([]); // No directory specified
        
        // Test would verify that the i18n directory was found and used
      } finally {
        await fs.rm(testDir, { recursive: true, force: true });
      }
    });

    it('should prefer locales over other default directories', async () => {
      // Create multiple default directories
      const localesDir = join(process.cwd(), 'locales');
      const i18nDir = join(process.cwd(), 'i18n');
      
      await fs.mkdir(localesDir, { recursive: true });
      await fs.mkdir(i18nDir, { recursive: true });
      await fs.writeFile(join(localesDir, 'en.json'), '{}');
      await fs.writeFile(join(i18nDir, 'en.json'), '{}');
      
      try {
        setupArgs([]);
        
        // Test would verify that locales was chosen over i18n
      } finally {
        await fs.rm(localesDir, { recursive: true, force: true });
        await fs.rm(i18nDir, { recursive: true, force: true });
      }
    });
  });
});

describe('Module Exports', () => {
  it('should export TranslationMCPServer class', async () => {
    const module = await import('../../src/index.js');
    expect(module.TranslationMCPServer).toBeDefined();
    expect(typeof module.TranslationMCPServer).toBe('function');
  });

  it('should export core types', async () => {
    const module = await import('../../src/index.js');
    
    // These should be available as type exports (not runtime)
    // In a real test, you might check that TypeScript compilation works
    expect(module).toBeDefined();
  });

  it('should export core classes', async () => {
    const module = await import('../../src/index.js');
    
    expect(module.TranslationIndex).toBeDefined();
    expect(module.TranslationFileWatcher).toBeDefined();
  });
});

/**
 * Note: Many of these tests are incomplete because the current index.ts structure
 * doesn't easily allow for unit testing. To make this more testable, consider:
 * 
 * 1. Extracting parseArgs, loadEnvConfig, resolveConfig into separate testable functions
 * 2. Making the main function accept dependencies (server instance, process, console)
 * 3. Separating the argument parsing logic from the server startup logic
 * 4. Using dependency injection for better testability
 * 
 * Example refactoring:
 * 
 * ```typescript
 * export function parseArgs(argv: string[]) { ... }
 * export function loadEnvConfig(env: NodeJS.ProcessEnv) { ... }
 * export function resolveConfig(args: any, env: any) { ... }
 * 
 * export async function main(deps = { process, console, TranslationMCPServer }) {
 *   // Main logic here
 * }
 * ```
 */
