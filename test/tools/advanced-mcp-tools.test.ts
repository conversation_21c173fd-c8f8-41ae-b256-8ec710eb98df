/**
 * Tests for advanced MCP tools
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TranslationIndex } from '../../src/core/translation-index.js';
import { TranslationFileWatcher } from '../../src/core/file-watcher.js';

// Mock the tool setup functions
const mockServer = {
  tool: vi.fn()
};

describe('Advanced MCP Tools', () => {
  let index: TranslationIndex;
  let fileWatcher: TranslationFileWatcher;
  let config: any;

  beforeEach(() => {
    index = new TranslationIndex({
      baseLanguage: 'en',
      cacheSize: 100
    });
    
    fileWatcher = new TranslationFileWatcher('./test-locales', {
      debounceMs: 100
    });
    
    config = {
      baseLanguage: 'en',
      translationDir: './test-locales',
      srcDir: './src',
      keyStyle: 'nested',
      autoSync: false
    };

    // Add test data
    index.set('common.submit', 'en', 'Submit');
    index.set('common.cancel', 'en', 'Cancel');
    index.set('auth.login', 'en', 'Login');

    vi.clearAllMocks();
  });

  describe('analyze_code_file tool', () => {
    it('should register analyze_code_file tool', async () => {
      const { setupAnalyzeCodeTool } = await import('../../src/tools/analyze-code-file.js');
      setupAnalyzeCodeTool(mockServer, index);

      expect(mockServer.tool).toHaveBeenCalledWith(
        'analyze_code_file',
        expect.stringContaining('Analyze source code file'),
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should analyze React code correctly', async () => {
      const { setupAnalyzeCodeTool } = await import('../../src/tools/analyze-code-file.js');
      setupAnalyzeCodeTool(mockServer, index);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'analyze_code_file');
      const handler = toolCall[3];

      // Mock file content
      const reactCode = `
        import React from 'react';
        export function Button() {
          return (
            <div>
              <button>{t('common.submit')}</button>
              <span>Hardcoded text here</span>
              <p>{t('missing.key')}</p>
            </div>
          );
        }
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(reactCode)
        }
      }));

      const result = await handler({
        filePath: 'Button.tsx',
        extractHardcoded: true,
        findUsage: true,
        frameworks: ['react'],
        minStringLength: 3,
        excludePatterns: []
      });

      let data;
      try {
        data = JSON.parse(result.content[0].text);
      } catch (error) {
        // If JSON parsing fails, the tool returned an error message
        expect(result.content[0].text).toContain('Error');
        return;
      }
      expect(data.detectedFramework).toBe('react');
      expect(data.summary.totalHardcodedStrings).toBeGreaterThan(0);
      expect(data.summary.translationUsageCount).toBeGreaterThan(0);
      expect(data.summary.missingTranslations).toBeGreaterThan(0);
    });
  });

  describe('extract_to_translation tool', () => {
    it('should register extract_to_translation tool', async () => {
      const { setupExtractToTranslationTool } = await import('../../src/tools/extract-to-translation.js');
      setupExtractToTranslationTool(mockServer, index, config);

      expect(mockServer.tool).toHaveBeenCalledWith(
        'extract_to_translation',
        expect.stringContaining('Extract hardcoded text'),
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should extract and replace hardcoded text', async () => {
      const { setupExtractToTranslationTool } = await import('../../src/tools/extract-to-translation.js');
      setupExtractToTranslationTool(mockServer, index, config);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'extract_to_translation');
      const handler = toolCall[3];

      const originalContent = '<button>Click me</button>';
      const mockReadFile = vi.fn().mockResolvedValue(originalContent);
      const mockWriteFile = vi.fn().mockResolvedValue(undefined);

      vi.doMock('fs', () => ({
        promises: {
          readFile: mockReadFile,
          writeFile: mockWriteFile
        }
      }));

      const result = await handler({
        filePath: 'Button.tsx',
        textToExtract: 'Click me',
        targetKey: 'common.click_me',
        replaceInFile: true,
        framework: 'react',
        baseLanguage: 'en',
        additionalLanguages: { es: 'Haz clic' },
        checkSimilar: false,
        keyStyle: 'nested'
      });

      let data;
      try {
        data = JSON.parse(result.content[0].text);
      } catch (error) {
        // If JSON parsing fails, the tool returned an error message
        expect(result.content[0].text).toContain('Error');
        return;
      }
      expect(data.success).toBe(true);
      expect(data.key).toBe('common.click_me');
      expect(data.addedLanguages).toContain('en');
      expect(data.addedLanguages).toContain('es');
    });

    it('should detect existing keys', async () => {
      const { setupExtractToTranslationTool } = await import('../../src/tools/extract-to-translation.js');
      setupExtractToTranslationTool(mockServer, index, config);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'extract_to_translation');
      const handler = toolCall[3];

      const result = await handler({
        filePath: 'Button.tsx',
        textToExtract: 'Submit',
        targetKey: 'common.submit', // This key already exists
        replaceInFile: false,
        checkSimilar: false
      });

      const data = JSON.parse(result.content[0].text);
      expect(data.error).toContain('Translation key already exists');
      expect(data.key).toBe('common.submit');
    });
  });

  describe('add_translation_smart tool', () => {
    it('should register add_translation_smart tool', async () => {
      const { setupAddTranslationSmartTool } = await import('../../src/tools/add-translation-smart.js');
      setupAddTranslationSmartTool(mockServer, index, config);

      expect(mockServer.tool).toHaveBeenCalledWith(
        'add_translation_smart',
        expect.stringContaining('Intelligently add single or multiple translations'),
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should add translation with auto-generated key', async () => {
      const { setupAddTranslationSmartTool } = await import('../../src/tools/add-translation-smart.js');
      setupAddTranslationSmartTool(mockServer, index, config);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'add_translation_smart');
      const handler = toolCall[3];

      const result = await handler({
        text: 'Save changes',
        languages: { en: 'Save changes', es: 'Guardar cambios' },
        autoGenerateKey: true,
        keyStyle: 'nested',
        checkSimilar: false,
        overwrite: false,
        validateStructure: false
      });

      const data = JSON.parse(result.content[0].text);
      expect(data.success).toBe(true);
      expect(data.key).toMatch(/^common\./);
      expect(data.addedLanguages).toContain('en');
      expect(data.addedLanguages).toContain('es');
    });

    it('should validate key format', async () => {
      const { setupAddTranslationSmartTool } = await import('../../src/tools/add-translation-smart.js');
      setupAddTranslationSmartTool(mockServer, index, config);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'add_translation_smart');
      const handler = toolCall[3];

      const result = await handler({
        text: 'Save changes',
        languages: { en: 'Save changes' },
        suggestedKey: 'Invalid.Key.Format',
        autoGenerateKey: false,
        keyStyle: 'nested',
        checkSimilar: false,
        overwrite: false,
        validateStructure: false
      });

      const data = JSON.parse(result.content[0].text);
      expect(data.error).toBeDefined();
      expect(data.error).toContain('Invalid key format');
    });
  });

  describe('get_translation_suggestions tool', () => {
    it('should register get_translation_suggestions tool', async () => {
      const { setupGetTranslationSuggestionsTool } = await import('../../src/tools/get-translation-suggestions.js');
      setupGetTranslationSuggestionsTool(mockServer, index, config);

      expect(mockServer.tool).toHaveBeenCalledWith(
        'get_translation_suggestions',
        expect.stringContaining('Get translation key suggestions'),
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should provide autocomplete suggestions', async () => {
      const { setupGetTranslationSuggestionsTool } = await import('../../src/tools/get-translation-suggestions.js');
      setupGetTranslationSuggestionsTool(mockServer, index, config);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'get_translation_suggestions');
      const handler = toolCall[3];

      const result = await handler({
        partial: 'common.',
        maxSuggestions: 10,
        includeValues: true,
        includeMetadata: false,
        sortBy: 'relevance'
      });

      const data = JSON.parse(result.content[0].text);
      expect(data.suggestions.length).toBeGreaterThan(0);
      expect(data.suggestions[0].keyPath).toMatch(/^common\./);
      expect(data.suggestions[0].value).toBeDefined();
      expect(data.context.totalMatches).toBeGreaterThan(0);
    });

    it('should filter suggestions by criteria', async () => {
      const { setupGetTranslationSuggestionsTool } = await import('../../src/tools/get-translation-suggestions.js');
      setupGetTranslationSuggestionsTool(mockServer, index, config);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'get_translation_suggestions');
      const handler = toolCall[3];

      const result = await handler({
        partial: 'common',
        maxSuggestions: 5,
        includeValues: true,
        filterBy: {
          hasValue: true,
          language: 'en'
        },
        sortBy: 'alphabetical'
      });

      const data = JSON.parse(result.content[0].text);
      expect(data.suggestions.length).toBeLessThanOrEqual(5);
      data.suggestions.forEach((suggestion: any) => {
        expect(suggestion.value).toBeDefined();
      });
    });
  });

  describe('sync_translations_to_files tool', () => {
    it('should register sync_translations_to_files tool', async () => {
      const { setupSyncTranslationsToFilesTool } = await import('../../src/tools/sync-translations-to-files.js');
      setupSyncTranslationsToFilesTool(mockServer, index, fileWatcher, config);

      expect(mockServer.tool).toHaveBeenCalledWith(
        'sync_translations_to_files',
        expect.stringContaining('Sync translation index back to translation files'),
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should perform dry run sync', async () => {
      const { setupSyncTranslationsToFilesTool } = await import('../../src/tools/sync-translations-to-files.js');
      setupSyncTranslationsToFilesTool(mockServer, index, fileWatcher, config);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'sync_translations_to_files');
      const handler = toolCall[3];

      // Mock file operations
      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockRejectedValue(new Error('File not found')),
          writeFile: vi.fn().mockResolvedValue(undefined),
          mkdir: vi.fn().mockResolvedValue(undefined)
        }
      }));

      const result = await handler({
        languages: ['en'],
        dryRun: true,
        backup: false,
        format: 'json',
        createMissing: true,
        sortKeys: true,
        indent: 2
      });

      const data = JSON.parse(result.content[0].text);
      expect(data.dryRun).toBe(true);
      expect(data.summary.totalLanguages).toBe(1);
      expect(data.results[0].language).toBe('en');
    });
  });

  describe('generate_types tool', () => {
    it('should register generate_types tool', async () => {
      const { setupGenerateTypesTool } = await import('../../src/tools/generate-types.js');
      setupGenerateTypesTool(mockServer, index, config);

      expect(mockServer.tool).toHaveBeenCalledWith(
        'generate_types',
        expect.stringContaining('Generate TypeScript types'),
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should generate TypeScript types', async () => {
      const { setupGenerateTypesTool } = await import('../../src/tools/generate-types.js');
      setupGenerateTypesTool(mockServer, index, config);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'generate_types');
      const handler = toolCall[3];

      // Mock file operations
      const mockWriteFile = vi.fn().mockResolvedValue(undefined);
      const mockMkdir = vi.fn().mockResolvedValue(undefined);
      const mockStat = vi.fn().mockResolvedValue({ size: 1024 });

      vi.doMock('fs', () => ({
        promises: {
          writeFile: mockWriteFile,
          mkdir: mockMkdir,
          stat: mockStat
        }
      }));

      const result = await handler({
        outputPath: './types/i18n.ts',
        namespace: 'I18n',
        includeValues: false,
        strict: true,
        baseLanguage: 'en',
        watch: false,
        validate: false
      });

      const data = JSON.parse(result.content[0].text);
      expect(data.success).toBe(true);
      expect(data.outputPath).toBe('./types/i18n.ts');
      expect(data.namespace).toBe('I18n');
      expect(data.stats.totalKeys).toBeGreaterThan(0);
    });

    it('should handle missing output path', async () => {
      const { setupGenerateTypesTool } = await import('../../src/tools/generate-types.js');
      const configWithoutTypes = { ...config };
      delete configWithoutTypes.generateTypes;
      
      setupGenerateTypesTool(mockServer, index, configWithoutTypes);

      const toolCall = mockServer.tool.mock.calls.find(call => call[0] === 'generate_types');
      const handler = toolCall[3];

      const result = await handler({
        namespace: 'I18n',
        includeValues: false,
        strict: true,
        baseLanguage: 'en',
        watch: false,
        validate: false
      });

      const data = JSON.parse(result.content[0].text);
      expect(data.error).toContain('No output path specified');
    });
  });
});
