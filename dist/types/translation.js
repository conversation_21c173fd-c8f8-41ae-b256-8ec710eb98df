/**
 * Core type definitions for the i18n MCP server
 */
/**
 * Error types for better error handling
 */
export class TranslationError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'TranslationError';
    }
    /**
     * Custom JSON serialization to include error properties
     */
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            details: this.details,
            stack: this.stack
        };
    }
}
export class ValidationError extends TranslationError {
    constructor(message, details) {
        super(message, 'VALIDATION_ERROR', details);
        this.name = 'ValidationError';
    }
}
export class FileWatchError extends TranslationError {
    constructor(message, details) {
        super(message, 'FILE_WATCH_ERROR', details);
        this.name = 'FileWatchError';
    }
}
export class IndexError extends TranslationError {
    constructor(message, details) {
        super(message, 'INDEX_ERROR', details);
        this.name = 'IndexError';
    }
}
//# sourceMappingURL=translation.js.map